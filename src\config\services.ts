import type { AppConfig, ServiceConfig } from '../types';

// 默认服务配置 - 您可以根据实际需求修改这些配置
const createDefaultServices = (baseHost: string): ServiceConfig[] => [
  {
    id: 'service-1',
    name: '用户管理系统',
    description: '用户注册、登录、权限管理等功能',
    host: baseHost,
    port: 3001,
    path: '',
    icon: 'Users',
    color: 'blue',
    status: 'online',
    features: [
      {
        id: 'user-list',
        name: '用户列表',
        description: '查看和管理所有用户',
        icon: 'UserCheck',
        path: '/users',
        category: '用户管理'
      },
      {
        id: 'user-roles',
        name: '角色管理',
        description: '配置用户角色和权限',
        icon: 'Shield',
        path: '/roles',
        category: '权限管理'
      },
      {
        id: 'user-analytics',
        name: '用户分析',
        description: '用户行为和统计分析',
        icon: 'BarChart3',
        path: '/analytics',
        category: '数据分析'
      },
      {
        id: 'user-settings',
        name: '系统设置',
        description: '用户系统相关配置',
        icon: 'Settings',
        path: '/settings',
        category: '系统管理'
      }
    ]
  },
  {
    id: 'service-2',
    name: '内容管理系统',
    description: '文章、媒体、内容发布管理',
    host: baseHost,
    port: 3002,
    path: '',
    icon: 'FileText',
    color: 'green',
    status: 'online',
    features: [
      {
        id: 'content-posts',
        name: '文章管理',
        description: '创建、编辑和发布文章',
        icon: 'Edit3',
        path: '/posts',
        category: '内容管理'
      },
      {
        id: 'content-media',
        name: '媒体库',
        description: '图片、视频等媒体文件管理',
        icon: 'Image',
        path: '/media',
        category: '媒体管理'
      },
      {
        id: 'content-categories',
        name: '分类管理',
        description: '内容分类和标签管理',
        icon: 'Tag',
        path: '/categories',
        category: '分类管理'
      },
      {
        id: 'content-comments',
        name: '评论管理',
        description: '用户评论审核和管理',
        icon: 'MessageSquare',
        path: '/comments',
        category: '互动管理'
      }
    ]
  },
  {
    id: 'service-3',
    name: '数据分析系统',
    description: '业务数据统计、报表生成',
    host: baseHost,
    port: 3003,
    path: '',
    icon: 'TrendingUp',
    color: 'purple',
    status: 'online',
    features: [
      {
        id: 'analytics-dashboard',
        name: '数据仪表板',
        description: '实时数据监控面板',
        icon: 'Monitor',
        path: '/dashboard',
        category: '数据监控'
      },
      {
        id: 'analytics-reports',
        name: '报表生成',
        description: '自定义报表和数据导出',
        icon: 'FileBarChart',
        path: '/reports',
        category: '报表管理'
      },
      {
        id: 'analytics-metrics',
        name: '指标管理',
        description: '业务指标配置和监控',
        icon: 'Target',
        path: '/metrics',
        category: '指标管理'
      },
      {
        id: 'analytics-alerts',
        name: '告警设置',
        description: '数据异常告警配置',
        icon: 'AlertTriangle',
        path: '/alerts',
        category: '告警管理'
      }
    ]
  }
];

// 应用配置
export const appConfig: AppConfig = {
  development: {
    name: '开发环境',
    services: createDefaultServices('localhost')
  },
  production: {
    name: '生产环境',
    services: createDefaultServices('your-production-domain.com') // 请替换为您的生产域名
  }
};

// 获取当前环境配置
export const getCurrentEnvironment = () => {
  return import.meta.env.MODE === 'production' ? 'production' : 'development';
};

// 获取当前环境的服务配置
export const getCurrentServices = (): ServiceConfig[] => {
  const env = getCurrentEnvironment();
  return appConfig[env].services;
};

// 根据ID获取服务配置
export const getServiceById = (serviceId: string): ServiceConfig | undefined => {
  const services = getCurrentServices();
  return services.find(service => service.id === serviceId);
};

// 生成服务URL
export const getServiceUrl = (service: ServiceConfig, path: string = ''): string => {
  const protocol = getCurrentEnvironment() === 'production' ? 'https' : 'http';
  const basePath = service.path || '';
  return `${protocol}://${service.host}:${service.port}${basePath}${path}`;
};
