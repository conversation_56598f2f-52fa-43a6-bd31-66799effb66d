/**
 * 服务配置测试
 * 
 * 这个文件包含了一些基本的测试用例，用于验证服务配置的正确性
 * 在实际项目中，建议使用 Jest 或 Vitest 等测试框架
 */

import { getCurrentServices, getServiceById, getServiceUrl } from '../config/services';

// 模拟测试函数
const test = (name: string, fn: () => void) => {
  try {
    fn();
    console.log(`✅ ${name}`);
  } catch (error) {
    console.error(`❌ ${name}:`, error);
  }
};

const expect = (actual: any) => ({
  toBe: (expected: any) => {
    if (actual !== expected) {
      throw new Error(`Expected ${expected}, but got ${actual}`);
    }
  },
  toBeDefined: () => {
    if (actual === undefined) {
      throw new Error('Expected value to be defined');
    }
  },
  toContain: (expected: any) => {
    if (!actual.includes(expected)) {
      throw new Error(`Expected ${actual} to contain ${expected}`);
    }
  }
});

// 运行测试
export const runTests = () => {
  console.log('🧪 Running service configuration tests...\n');

  test('should get current services', () => {
    const services = getCurrentServices();
    expect(services).toBeDefined();
    expect(services.length).toBe(3);
  });

  test('should get service by ID', () => {
    const service = getServiceById('service-1');
    expect(service).toBeDefined();
    expect(service?.name).toBe('用户管理系统');
  });

  test('should generate correct service URL', () => {
    const service = getServiceById('service-1');
    if (service) {
      const url = getServiceUrl(service, '/test');
      expect(url).toContain('localhost:3001');
      expect(url).toContain('/test');
    }
  });

  test('should have valid service features', () => {
    const services = getCurrentServices();
    services.forEach(service => {
      expect(service.features.length).toBe(4);
      service.features.forEach(feature => {
        expect(feature.id).toBeDefined();
        expect(feature.name).toBeDefined();
        expect(feature.path).toBeDefined();
      });
    });
  });

  console.log('\n✨ All tests completed!');
};

// 在开发环境中自动运行测试
if (import.meta.env.DEV) {
  // runTests();
}
