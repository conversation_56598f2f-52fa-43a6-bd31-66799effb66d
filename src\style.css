@import "tailwindcss";

* {
  box-sizing: border-box;
}

body {
  background-color: #f9fafb;
  color: #111827;
  min-height: 100vh;
  font-feature-settings: "rlig" 1, "calt" 1;
  margin: 0;
}

#app {
  min-height: 100vh;
}

/* 自定义组件样式 */
.service-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.service-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-0.25rem);
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #374151;
  transition: colors 0.2s ease;
  cursor: pointer;
  border-radius: 0.375rem;
  margin: 0.25rem 0.5rem;
}

.sidebar-item:hover {
  background-color: #f3f4f6;
  color: #2563eb;
}

.sidebar-item.active {
  background-color: #eff6ff;
  color: #1d4ed8;
  border-left: 4px solid #2563eb;
}

.page-container {
  min-height: 100vh;
  background-color: #f9fafb;
}

.main-content {
  flex: 1;
  padding: 1.5rem;
  overflow: auto;
}

/* 响应式工具类 */
.responsive-grid {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 1rem;
}

@media (min-width: 640px) {
  .responsive-grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1280px) {
  .responsive-grid {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

.responsive-padding {
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .responsive-padding {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .responsive-padding {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
