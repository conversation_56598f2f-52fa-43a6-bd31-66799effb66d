@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gray-50 text-gray-900 min-h-screen;
    font-feature-settings: "rlig" 1, "calt" 1;
    margin: 0;
  }

  #app {
    @apply min-h-screen;
  }
}

@layer components {
  .service-card {
    @apply bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 p-6 border border-gray-200 cursor-pointer relative overflow-hidden;
  }

  .service-card:hover {
    @apply transform -translate-y-1;
  }

  .sidebar-item {
    @apply flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 hover:text-primary-600 transition-colors duration-200 cursor-pointer rounded-md mx-2 my-1;
  }

  .sidebar-item.active {
    @apply bg-primary-50 text-primary-700 border-l-4 border-primary-600;
  }

  .page-container {
    @apply min-h-screen bg-gray-50;
  }

  .main-content {
    @apply flex-1 p-6 overflow-auto;
  }

  /* 响应式工具类 */
  .responsive-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
  }

  .responsive-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .responsive-text {
    @apply text-sm sm:text-base;
  }

  /* 文本截断 */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
