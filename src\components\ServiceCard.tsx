import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ServiceConfig } from '../types';
import { getServiceColorClasses, getStatusColorClasses } from '../utils';
import Icon, { IconName } from './Icon';

interface ServiceCardProps {
  service: ServiceConfig;
  onClick?: (service: ServiceConfig) => void;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ service, onClick }) => {
  const navigate = useNavigate();
  const colorClasses = getServiceColorClasses(service.color);
  const statusClasses = getStatusColorClasses(service.status);

  const handleClick = () => {
    if (onClick) {
      onClick(service);
    } else {
      navigate(`/service/${service.id}`);
    }
  };

  const handleExternalClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    const protocol = import.meta.env.MODE === 'production' ? 'https' : 'http';
    const url = `${protocol}://${service.host}:${service.port}${service.path || ''}`;
    window.open(url, '_blank');
  };

  return (
    <div 
      className={`service-card ${colorClasses.bg} ${colorClasses.border} ${colorClasses.hover} group`}
      onClick={handleClick}
    >
      {/* 卡片头部 */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={`p-3 rounded-lg ${colorClasses.bg} ${colorClasses.border}`}>
            <Icon 
              name={service.icon as IconName || 'Server'} 
              size={24} 
              className={colorClasses.text}
            />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 group-hover:text-gray-700">
              {service.name}
            </h3>
            <div className="flex items-center space-x-2 mt-1">
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusClasses.bg} ${statusClasses.text}`}>
                <span className={`w-2 h-2 rounded-full mr-1 ${statusClasses.dot}`}></span>
                {service.status === 'online' ? '在线' : 
                 service.status === 'offline' ? '离线' : '维护中'}
              </span>
              <span className="text-xs text-gray-500">
                {service.host}:{service.port}
              </span>
            </div>
          </div>
        </div>
        
        {/* 外部链接按钮 */}
        <button
          onClick={handleExternalClick}
          className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-2 rounded-lg hover:bg-gray-100"
          title="在新窗口中打开"
        >
          <Icon name="ExternalLink" size={16} className="text-gray-500" />
        </button>
      </div>

      {/* 服务描述 */}
      <p className="text-gray-600 text-sm mb-4 line-clamp-2">
        {service.description}
      </p>

      {/* 功能统计 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 text-sm text-gray-500">
          <span className="flex items-center">
            <Icon name="Zap" size={14} className="mr-1" />
            {service.features.length} 个功能
          </span>
          {service.status === 'online' && (
            <span className="flex items-center">
              <Icon name="Activity" size={14} className="mr-1" />
              运行中
            </span>
          )}
        </div>
        
        <Icon 
          name="ChevronRight" 
          size={16} 
          className="text-gray-400 group-hover:text-gray-600 transition-colors duration-200" 
        />
      </div>

      {/* 悬停效果 */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent to-transparent group-hover:from-white/5 group-hover:to-white/10 rounded-lg transition-all duration-300 pointer-events-none"></div>
    </div>
  );
};

export default ServiceCard;
