import React, { useState } from 'react';
import { ServiceFeature, ServiceConfig } from '../types';
import { getServiceColorClasses, buildServiceUrl } from '../utils';
import Icon, { IconName } from './Icon';

interface FeatureContentProps {
  feature: ServiceFeature;
  service: ServiceConfig;
}

const FeatureContent: React.FC<FeatureContentProps> = ({ feature, service }) => {
  const [isLoading, setIsLoading] = useState(false);
  const colorClasses = getServiceColorClasses(service.color);

  const handleOpenInService = () => {
    const url = buildServiceUrl(service, feature.path);
    window.open(url, '_blank');
  };

  const handleTestConnection = async () => {
    setIsLoading(true);
    try {
      const url = buildServiceUrl(service, feature.path);
      const response = await fetch(url, { method: 'HEAD' });
      // 这里可以添加连接测试的逻辑
      console.log('Connection test result:', response.ok);
    } catch (error) {
      console.error('Connection test failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6">
      {/* 功能头部 */}
      <div className="mb-6">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-4">
            <div className={`p-3 rounded-lg ${colorClasses.bg}`}>
              <Icon 
                name={feature.icon as IconName || 'Zap'} 
                size={32} 
                className={colorClasses.text}
              />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{feature.name}</h1>
              <p className="text-gray-600 mt-1">{feature.description}</p>
              <div className="flex items-center space-x-4 mt-2">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colorClasses.bg} ${colorClasses.text}`}>
                  {feature.category || '其他'}
                </span>
                <span className="text-sm text-gray-500">
                  路径: {feature.path}
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleTestConnection}
              disabled={isLoading}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              <Icon 
                name={isLoading ? 'Refresh' : 'Activity'} 
                size={16} 
                className={`mr-2 ${isLoading ? 'animate-spin' : ''}`}
              />
              {isLoading ? '测试中...' : '测试连接'}
            </button>
            
            <button
              onClick={handleOpenInService}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white ${colorClasses.text.replace('text-', 'bg-').replace('-700', '-600')} hover:${colorClasses.text.replace('text-', 'bg-').replace('-700', '-700')} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500`}
            >
              <Icon name="ExternalLink" size={16} className="mr-2" />
              在服务中打开
            </button>
          </div>
        </div>
      </div>

      {/* 功能详情内容 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 主要信息 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 功能介绍 */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">功能介绍</h2>
            <div className="prose prose-sm max-w-none">
              <p className="text-gray-600">
                {feature.description}
              </p>
              
              {/* 这里可以根据不同的功能类型展示不同的内容 */}
              <div className="mt-4 space-y-3">
                <h3 className="text-base font-medium text-gray-900">主要特性</h3>
                <ul className="list-disc list-inside space-y-1 text-gray-600">
                  <li>高性能的数据处理能力</li>
                  <li>直观的用户界面设计</li>
                  <li>完善的权限控制机制</li>
                  <li>实时数据同步功能</li>
                </ul>
              </div>
            </div>
          </div>

          {/* 使用指南 */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">使用指南</h2>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                  <span className="text-xs font-medium text-primary-600">1</span>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900">访问功能</h4>
                  <p className="text-sm text-gray-600">点击"在服务中打开"按钮直接访问该功能</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                  <span className="text-xs font-medium text-primary-600">2</span>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900">配置权限</h4>
                  <p className="text-sm text-gray-600">确保您有足够的权限访问此功能</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                  <span className="text-xs font-medium text-primary-600">3</span>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900">开始使用</h4>
                  <p className="text-sm text-gray-600">根据界面提示进行相关操作</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 侧边信息 */}
        <div className="space-y-6">
          {/* 快速信息 */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">快速信息</h3>
            <dl className="space-y-3">
              <div>
                <dt className="text-sm font-medium text-gray-500">所属服务</dt>
                <dd className="text-sm text-gray-900">{service.name}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">功能分类</dt>
                <dd className="text-sm text-gray-900">{feature.category || '其他'}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">访问路径</dt>
                <dd className="text-sm text-gray-900 font-mono">{feature.path}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">服务地址</dt>
                <dd className="text-sm text-gray-900">{service.host}:{service.port}</dd>
              </div>
            </dl>
          </div>

          {/* 相关链接 */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">相关链接</h3>
            <div className="space-y-2">
              <a
                href={buildServiceUrl(service, feature.path)}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center text-sm text-primary-600 hover:text-primary-700"
              >
                <Icon name="ExternalLink" size={14} className="mr-2" />
                直接访问功能
              </a>
              <a
                href={buildServiceUrl(service)}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center text-sm text-primary-600 hover:text-primary-700"
              >
                <Icon name="Globe" size={14} className="mr-2" />
                访问服务首页
              </a>
            </div>
          </div>

          {/* 状态信息 */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">状态信息</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">服务状态</span>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  service.status === 'online' 
                    ? 'bg-green-100 text-green-800' 
                    : service.status === 'offline'
                    ? 'bg-red-100 text-red-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  <span className={`w-2 h-2 rounded-full mr-1 ${
                    service.status === 'online' 
                      ? 'bg-green-400' 
                      : service.status === 'offline'
                      ? 'bg-red-400'
                      : 'bg-yellow-400'
                  }`}></span>
                  {service.status === 'online' ? '在线' : 
                   service.status === 'offline' ? '离线' : '维护中'}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">功能状态</span>
                <span className="text-sm text-gray-900">
                  {feature.isActive !== false ? '可用' : '不可用'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeatureContent;
