# Spider Admin - 多服务管理平台

一个现代化的多服务管理平台，支持开发和生产环境配置，提供统一的服务入口和功能导航。

## ✨ 特性

- 🚀 **现代技术栈**: React 18 + TypeScript + Vite + Tailwind CSS
- 🎯 **多环境支持**: 开发环境和生产环境独立配置
- 🔧 **灵活配置**: 轻松添加新服务和功能
- 📱 **响应式设计**: 完美适配桌面端和移动端
- 🎨 **美观界面**: 现代化的 UI 设计和流畅的交互体验
- ⚡ **高性能**: 基于 Vite 的快速开发和构建
- 🔍 **智能搜索**: 支持服务和功能的快速搜索
- 📊 **状态监控**: 实时显示服务状态和统计信息

## 🛠️ 技术栈

- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite
- **样式框架**: Tailwind CSS
- **状态管理**: Zustand
- **路由管理**: React Router
- **图标库**: Lucide React
- **开发工具**: ESLint + Prettier

## 📦 安装和运行

### 环境要求

- Node.js >= 16.0.0
- npm >= 7.0.0

### 安装依赖

```bash
npm install
```

### 开发环境运行

```bash
npm run dev
```

应用将在 `http://localhost:5173` 启动

### 生产环境构建

```bash
npm run build
```

### 预览生产构建

```bash
npm run preview
```

## ⚙️ 配置说明

### 服务配置

1. 复制配置示例文件：
   ```bash
   cp src/config/services.example.ts src/config/services.ts
   ```

2. 编辑 `src/config/services.ts` 文件，配置您的服务信息：

```typescript
const createDefaultServices = (baseHost: string): ServiceConfig[] => [
  {
    id: 'your-service-id',
    name: '您的服务名称',
    description: '服务描述',
    host: baseHost,
    port: 3001, // 您的服务端口
    icon: 'Server', // 图标名称
    color: 'blue', // 主题颜色
    status: 'online',
    features: [
      {
        id: 'feature-id',
        name: '功能名称',
        description: '功能描述',
        icon: 'Zap',
        path: '/feature-path',
        category: '功能分类'
      }
    ]
  }
];
```

### 环境配置

- **开发环境**: 默认使用 `localhost` 作为服务主机
- **生产环境**: 需要在配置文件中设置实际的生产域名

### 添加新服务

1. 在 `createDefaultServices` 函数中添加新的服务配置
2. 设置唯一的服务 ID 和基本信息
3. 配置服务的功能列表
4. 选择合适的图标和颜色主题

## 🎨 自定义主题

### 颜色主题

支持以下预设颜色主题：
- `blue` - 蓝色主题
- `green` - 绿色主题  
- `purple` - 紫色主题
- `red` - 红色主题
- `yellow` - 黄色主题
- `indigo` - 靛蓝主题

### 图标选择

项目使用 Lucide React 图标库，支持的图标包括：
- `Users`, `UserCheck`, `Shield`, `Settings`
- `FileText`, `Edit3`, `Image`, `Tag`
- `TrendingUp`, `BarChart3`, `Monitor`, `Target`
- `Server`, `Database`, `Globe`, `Zap`

更多图标请查看 `src/components/Icon.tsx` 文件。

## 📱 响应式设计

- **桌面端**: 完整的功能展示和导航
- **平板端**: 优化的布局和交互
- **移动端**: 简化的界面和触摸友好的操作

## 🔧 开发指南

### 项目结构

```
src/
├── components/          # 可复用组件
│   ├── Icon.tsx        # 图标组件
│   ├── ServiceCard.tsx # 服务卡片
│   ├── Sidebar.tsx     # 侧边栏
│   └── FeatureContent.tsx # 功能内容
├── pages/              # 页面组件
│   ├── HomePage.tsx    # 首页
│   └── ServiceDetailPage.tsx # 服务详情页
├── store/              # 状态管理
│   └── useAppStore.ts  # 应用状态
├── config/             # 配置文件
│   ├── services.ts     # 服务配置
│   └── services.example.ts # 配置示例
├── types/              # 类型定义
│   └── index.ts        # 通用类型
├── utils/              # 工具函数
│   └── index.ts        # 通用工具
└── style.css           # 全局样式
```

### 添加新功能

1. 在对应服务的 `features` 数组中添加功能配置
2. 设置功能的路径、图标和分类
3. 如需自定义功能页面，可在 `FeatureContent.tsx` 中添加特殊处理

## 🚀 部署

### 静态部署

构建后的文件可以部署到任何静态文件服务器：

```bash
npm run build
# 将 dist/ 目录部署到您的服务器
```

### Docker 部署

```dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
EXPOSE 80
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 📄 许可证

MIT License

## 🆘 支持

如果您在使用过程中遇到问题，请：

1. 查看本 README 文档
2. 检查配置文件是否正确
3. 查看浏览器控制台的错误信息
4. 提交 Issue 描述问题

---

**Happy Coding! 🎉**
