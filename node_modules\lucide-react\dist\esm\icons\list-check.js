/**
 * @license lucide-react v0.532.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M11 18H3", key: "n3j2dh" }],
  ["path", { d: "m15 18 2 2 4-4", key: "1szwhi" }],
  ["path", { d: "M16 12H3", key: "1a2rj7" }],
  ["path", { d: "M16 6H3", key: "1wxfjs" }]
];
const ListCheck = createLucideIcon("list-check", __iconNode);

export { __iconNode, ListCheck as default };
//# sourceMappingURL=list-check.js.map
