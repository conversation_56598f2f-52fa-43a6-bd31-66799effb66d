import React, { useEffect, useState } from 'react';
import { useAppStore } from '../store/useAppStore';
import { getCurrentEnvironment } from '../config/services';
import ServiceCard from '../components/ServiceCard';
import Icon from '../components/Icon';

const HomePage: React.FC = () => {
  const { services, currentEnvironment, setEnvironment } = useAppStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // 获取所有功能分类
  const categories = React.useMemo(() => {
    const allCategories = new Set<string>();
    services.forEach(service => {
      service.features.forEach(feature => {
        if (feature.category) {
          allCategories.add(feature.category);
        }
      });
    });
    return Array.from(allCategories);
  }, [services]);

  // 过滤服务
  const filteredServices = React.useMemo(() => {
    return services.filter(service => {
      const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           service.description.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesCategory = selectedCategory === 'all' || 
                             service.features.some(feature => feature.category === selectedCategory);
      
      return matchesSearch && matchesCategory;
    });
  }, [services, searchTerm, selectedCategory]);

  // 环境切换
  const handleEnvironmentChange = (env: 'development' | 'production') => {
    setEnvironment(env);
  };

  return (
    <div className="page-container">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Icon name="Server" size={32} className="text-primary-600" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">Spider Admin</h1>
                <p className="text-sm text-gray-500">多服务管理平台</p>
              </div>
            </div>
            
            {/* 环境切换 */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">环境:</span>
                <select
                  value={currentEnvironment}
                  onChange={(e) => handleEnvironmentChange(e.target.value as 'development' | 'production')}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="development">开发环境</option>
                  <option value="production">生产环境</option>
                </select>
              </div>
              
              <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                currentEnvironment === 'production' 
                  ? 'bg-red-100 text-red-800' 
                  : 'bg-green-100 text-green-800'
              }`}>
                {currentEnvironment === 'production' ? '生产' : '开发'}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 主内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 搜索和过滤 */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* 搜索框 */}
            <div className="flex-1 relative">
              <Icon 
                name="Search" 
                size={20} 
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" 
              />
              <input
                type="text"
                placeholder="搜索服务..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
            
            {/* 分类过滤 */}
            <div className="flex items-center space-x-2">
              <Icon name="Filter" size={20} className="text-gray-400" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="all">所有分类</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Icon name="Server" size={24} className="text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">总服务数</p>
                <p className="text-2xl font-bold text-gray-900">{services.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Icon name="Wifi" size={24} className="text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">在线服务</p>
                <p className="text-2xl font-bold text-gray-900">
                  {services.filter(s => s.status === 'online').length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Icon name="Zap" size={24} className="text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">总功能数</p>
                <p className="text-2xl font-bold text-gray-900">
                  {services.reduce((total, service) => total + service.features.length, 0)}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Icon name="Globe" size={24} className="text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">当前环境</p>
                <p className="text-lg font-bold text-gray-900">
                  {currentEnvironment === 'production' ? '生产环境' : '开发环境'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 服务列表 */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            服务列表 ({filteredServices.length})
          </h2>
          
          {filteredServices.length === 0 ? (
            <div className="text-center py-12">
              <Icon name="Search" size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">未找到匹配的服务</h3>
              <p className="text-gray-500">请尝试调整搜索条件或过滤器</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredServices.map((service) => (
                <ServiceCard key={service.id} service={service} />
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default HomePage;
