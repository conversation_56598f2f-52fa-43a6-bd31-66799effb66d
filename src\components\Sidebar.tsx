import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ServiceConfig, ServiceFeature } from '../types';
import { useAppStore } from '../store/useAppStore';
import { getServiceColorClasses } from '../utils';
import Icon, { IconName } from './Icon';

interface SidebarProps {
  service: ServiceConfig;
  currentFeatureId?: string;
  onFeatureSelect?: (feature: ServiceFeature) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ 
  service, 
  currentFeatureId, 
  onFeatureSelect 
}) => {
  const navigate = useNavigate();
  const { sidebarCollapsed, toggleSidebar, setCurrentFeature } = useAppStore();
  const colorClasses = getServiceColorClasses(service.color);

  // 按分类分组功能
  const groupedFeatures = React.useMemo(() => {
    const groups: Record<string, ServiceFeature[]> = {};
    
    service.features.forEach(feature => {
      const category = feature.category || '其他';
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(feature);
    });
    
    return groups;
  }, [service.features]);

  const handleFeatureClick = (feature: ServiceFeature) => {
    setCurrentFeature(feature.id);
    if (onFeatureSelect) {
      onFeatureSelect(feature);
    }
    navigate(`/service/${service.id}/feature/${feature.id}`);
  };

  const handleBackToHome = () => {
    navigate('/');
  };

  return (
    <div className={`bg-white border-r border-gray-200 transition-all duration-300 ${
      sidebarCollapsed ? 'w-16' : 'w-64'
    }`}>
      {/* 侧边栏头部 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          {!sidebarCollapsed && (
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${colorClasses.bg}`}>
                <Icon 
                  name={service.icon as IconName || 'Server'} 
                  size={20} 
                  className={colorClasses.text}
                />
              </div>
              <div>
                <h2 className="font-semibold text-gray-900 text-sm truncate">
                  {service.name}
                </h2>
                <p className="text-xs text-gray-500 truncate">
                  {service.host}:{service.port}
                </p>
              </div>
            </div>
          )}
          
          <button
            onClick={toggleSidebar}
            className="p-1 rounded-md hover:bg-gray-100 transition-colors duration-200"
            title={sidebarCollapsed ? '展开侧边栏' : '收起侧边栏'}
          >
            <Icon 
              name={sidebarCollapsed ? 'ChevronRight' : 'Menu'} 
              size={16} 
              className="text-gray-500" 
            />
          </button>
        </div>
      </div>

      {/* 导航按钮 */}
      <div className="p-2 border-b border-gray-200">
        <button
          onClick={handleBackToHome}
          className={`w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors duration-200 ${
            sidebarCollapsed ? 'justify-center' : 'justify-start'
          }`}
          title="返回首页"
        >
          <Icon name="Home" size={16} className="text-gray-500" />
          {!sidebarCollapsed && <span className="ml-3">返回首页</span>}
        </button>
      </div>

      {/* 功能菜单 */}
      <div className="flex-1 overflow-y-auto">
        {Object.entries(groupedFeatures).map(([category, features]) => (
          <div key={category} className="py-2">
            {!sidebarCollapsed && (
              <div className="px-4 py-2">
                <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  {category}
                </h3>
              </div>
            )}
            
            <div className="space-y-1 px-2">
              {features.map((feature) => (
                <button
                  key={feature.id}
                  onClick={() => handleFeatureClick(feature)}
                  className={`w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors duration-200 ${
                    currentFeatureId === feature.id
                      ? `${colorClasses.bg} ${colorClasses.text} border-l-4 ${colorClasses.border.replace('border-', 'border-l-')}`
                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                  } ${sidebarCollapsed ? 'justify-center' : 'justify-start'}`}
                  title={sidebarCollapsed ? feature.name : feature.description}
                >
                  <Icon 
                    name={feature.icon as IconName || 'Zap'} 
                    size={16} 
                    className={currentFeatureId === feature.id ? colorClasses.text : 'text-gray-500'}
                  />
                  {!sidebarCollapsed && (
                    <span className="ml-3 truncate">{feature.name}</span>
                  )}
                </button>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* 侧边栏底部 */}
      {!sidebarCollapsed && (
        <div className="p-4 border-t border-gray-200">
          <div className="text-xs text-gray-500 space-y-1">
            <div className="flex items-center justify-between">
              <span>服务状态:</span>
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                service.status === 'online' 
                  ? 'bg-green-100 text-green-800' 
                  : service.status === 'offline'
                  ? 'bg-red-100 text-red-800'
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {service.status === 'online' ? '在线' : 
                 service.status === 'offline' ? '离线' : '维护中'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span>功能数量:</span>
              <span className="font-medium">{service.features.length}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Sidebar;
