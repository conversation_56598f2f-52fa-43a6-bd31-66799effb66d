import type { AppConfig, ServiceConfig } from '../types';

/**
 * 服务配置示例文件
 * 
 * 使用说明：
 * 1. 复制此文件为 services.ts
 * 2. 根据您的实际服务配置修改以下内容
 * 3. 更新服务名称、描述、端口号等信息
 * 4. 添加或删除服务功能
 * 5. 修改生产环境的域名
 */

// 创建服务配置的辅助函数
const createDefaultServices = (baseHost: string): ServiceConfig[] => [
  {
    id: 'user-service',
    name: '用户管理系统',
    description: '负责用户注册、登录、权限管理等核心功能',
    host: baseHost,
    port: 3001, // 修改为您的实际端口
    path: '', // 如果服务有子路径，在这里配置
    icon: 'Users',
    color: 'blue',
    status: 'online',
    features: [
      {
        id: 'user-list',
        name: '用户列表',
        description: '查看和管理所有注册用户',
        icon: 'UserCheck',
        path: '/users',
        category: '用户管理'
      },
      {
        id: 'user-roles',
        name: '角色权限',
        description: '配置用户角色和权限设置',
        icon: 'Shield',
        path: '/roles',
        category: '权限管理'
      },
      {
        id: 'user-analytics',
        name: '用户统计',
        description: '用户行为分析和数据统计',
        icon: 'BarChart3',
        path: '/analytics',
        category: '数据分析'
      }
    ]
  },
  {
    id: 'content-service',
    name: '内容管理系统',
    description: '文章发布、媒体管理、内容审核等功能',
    host: baseHost,
    port: 3002, // 修改为您的实际端口
    path: '',
    icon: 'FileText',
    color: 'green',
    status: 'online',
    features: [
      {
        id: 'content-posts',
        name: '文章管理',
        description: '创建、编辑和发布文章内容',
        icon: 'Edit3',
        path: '/posts',
        category: '内容管理'
      },
      {
        id: 'content-media',
        name: '媒体库',
        description: '上传和管理图片、视频等媒体文件',
        icon: 'Image',
        path: '/media',
        category: '媒体管理'
      },
      {
        id: 'content-categories',
        name: '分类标签',
        description: '管理内容分类和标签系统',
        icon: 'Tag',
        path: '/categories',
        category: '分类管理'
      }
    ]
  },
  {
    id: 'analytics-service',
    name: '数据分析系统',
    description: '业务数据统计、报表生成、监控告警',
    host: baseHost,
    port: 3003, // 修改为您的实际端口
    path: '',
    icon: 'TrendingUp',
    color: 'purple',
    status: 'online',
    features: [
      {
        id: 'analytics-dashboard',
        name: '数据仪表板',
        description: '实时业务数据监控面板',
        icon: 'Monitor',
        path: '/dashboard',
        category: '数据监控'
      },
      {
        id: 'analytics-reports',
        name: '报表中心',
        description: '生成和导出各类业务报表',
        icon: 'FileBarChart',
        path: '/reports',
        category: '报表管理'
      },
      {
        id: 'analytics-alerts',
        name: '告警设置',
        description: '配置数据异常告警规则',
        icon: 'AlertTriangle',
        path: '/alerts',
        category: '告警管理'
      }
    ]
  }
];

// 应用配置
export const appConfig: AppConfig = {
  development: {
    name: '开发环境',
    services: createDefaultServices('localhost')
  },
  production: {
    name: '生产环境',
    // 请将 'your-domain.com' 替换为您的实际生产域名
    services: createDefaultServices('your-domain.com')
  }
};

// 获取当前环境
export const getCurrentEnvironment = () => {
  return import.meta.env.MODE === 'production' ? 'production' : 'development';
};

// 获取当前环境的服务配置
export const getCurrentServices = (): ServiceConfig[] => {
  const env = getCurrentEnvironment();
  return appConfig[env].services;
};

// 根据ID获取服务配置
export const getServiceById = (serviceId: string): ServiceConfig | undefined => {
  const services = getCurrentServices();
  return services.find(service => service.id === serviceId);
};

// 生成服务URL
export const getServiceUrl = (service: ServiceConfig, path: string = ''): string => {
  const protocol = getCurrentEnvironment() === 'production' ? 'https' : 'http';
  const basePath = service.path || '';
  return `${protocol}://${service.host}:${service.port}${basePath}${path}`;
};

/**
 * 添加新服务的步骤：
 * 
 * 1. 在 createDefaultServices 函数中添加新的服务配置对象
 * 2. 设置唯一的 id、名称、描述等基本信息
 * 3. 配置正确的 host 和 port
 * 4. 选择合适的图标和颜色主题
 * 5. 添加服务的功能列表
 * 6. 为每个功能设置图标、路径和分类
 * 
 * 图标选项（来自 lucide-react）：
 * - Users, UserCheck, Shield, Settings
 * - FileText, Edit3, Image, Tag
 * - TrendingUp, BarChart3, Monitor, Target
 * - Server, Database, Globe, Zap
 * - 更多图标请查看 src/components/Icon.tsx
 * 
 * 颜色选项：
 * - blue, green, purple, red, yellow, indigo
 */
