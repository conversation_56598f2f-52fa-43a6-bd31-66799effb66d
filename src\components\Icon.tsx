import React from 'react';
import {
  Users,
  FileText,
  TrendingUp,
  UserCheck,
  Shield,
  BarChart3,
  Settings,
  Edit3,
  Image,
  Tag,
  MessageSquare,
  Monitor,
  FileBarChart,
  Target,
  AlertTriangle,
  Home,
  Menu,
  X,
  ChevronRight,
  ExternalLink,
  Wifi,
  WifiOff,
  Clock,
  Server,
  Database,
  Globe,
  Zap,
  Activity,
  Bell,
  Search,
  Filter,
  Download,
  Upload,
  Refresh,
  Plus,
  Minus,
  Check,
  AlertCircle,
  Info,
  HelpCircle
} from 'lucide-react';

// 图标映射
const iconMap = {
  Users,
  FileText,
  TrendingUp,
  UserCheck,
  Shield,
  BarChart3,
  Settings,
  Edit3,
  Image,
  Tag,
  MessageSquare,
  Monitor,
  FileBarChart,
  Target,
  AlertTriangle,
  Home,
  Menu,
  X,
  ChevronRight,
  ExternalLink,
  Wifi,
  WifiOff,
  Clock,
  Server,
  Database,
  Globe,
  Zap,
  Activity,
  Bell,
  Search,
  Filter,
  Download,
  Upload,
  Refresh,
  Plus,
  Minus,
  Check,
  AlertCircle,
  Info,
  HelpCircle
};

export type IconName = keyof typeof iconMap;

interface IconProps {
  name: IconName;
  size?: number;
  className?: string;
  color?: string;
}

const Icon: React.FC<IconProps> = ({ 
  name, 
  size = 20, 
  className = '', 
  color 
}) => {
  const IconComponent = iconMap[name];
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found`);
    return <HelpCircle size={size} className={className} color={color} />;
  }

  return (
    <IconComponent 
      size={size} 
      className={className} 
      color={color}
    />
  );
};

export default Icon;
