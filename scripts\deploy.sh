#!/bin/bash

# Spider Admin 部署脚本
# 使用方法: ./scripts/deploy.sh [environment]
# 环境选项: dev (开发) | prod (生产)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

# 检查参数
ENVIRONMENT=${1:-dev}

print_message "🚀 开始部署 Spider Admin..." $BLUE
print_message "📦 目标环境: $ENVIRONMENT" $YELLOW

# 检查 Node.js 和 npm
if ! command -v node &> /dev/null; then
    print_message "❌ Node.js 未安装，请先安装 Node.js" $RED
    exit 1
fi

if ! command -v npm &> /dev/null; then
    print_message "❌ npm 未安装，请先安装 npm" $RED
    exit 1
fi

# 安装依赖
print_message "📦 安装依赖..." $YELLOW
npm install

# 运行类型检查
print_message "🔍 运行类型检查..." $YELLOW
npx tsc --noEmit

# 构建项目
print_message "🔨 构建项目..." $YELLOW
if [ "$ENVIRONMENT" = "prod" ]; then
    npm run build
else
    npm run build
fi

# 检查构建结果
if [ ! -d "dist" ]; then
    print_message "❌ 构建失败，dist 目录不存在" $RED
    exit 1
fi

print_message "✅ 构建完成" $GREEN

# 根据环境执行不同的部署逻辑
case $ENVIRONMENT in
    "dev")
        print_message "🔧 开发环境部署..." $YELLOW
        print_message "💡 提示: 运行 'npm run preview' 来预览构建结果" $BLUE
        ;;
    "prod")
        print_message "🌐 生产环境部署..." $YELLOW
        print_message "📁 构建文件位于 dist/ 目录" $BLUE
        print_message "💡 提示: 将 dist/ 目录内容上传到您的服务器" $BLUE
        
        # 可以在这里添加自动部署到服务器的逻辑
        # 例如: rsync, scp, 或调用 CI/CD 工具
        ;;
    *)
        print_message "❌ 未知环境: $ENVIRONMENT" $RED
        print_message "💡 支持的环境: dev, prod" $YELLOW
        exit 1
        ;;
esac

print_message "🎉 部署完成!" $GREEN
print_message "📊 构建统计:" $BLUE

# 显示构建文件大小
if [ -d "dist" ]; then
    du -sh dist/*
fi

print_message "🔗 有用的命令:" $BLUE
print_message "  - 本地预览: npm run preview" $NC
print_message "  - 开发模式: npm run dev" $NC
print_message "  - 重新构建: npm run build" $NC
