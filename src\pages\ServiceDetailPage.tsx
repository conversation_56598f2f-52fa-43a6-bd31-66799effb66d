import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppStore, useCurrentService, useCurrentFeature } from '../store/useAppStore';
import { getServiceById } from '../config/services';
import Sidebar from '../components/Sidebar';
import FeatureContent from '../components/FeatureContent';
import Icon from '../components/Icon';

const ServiceDetailPage: React.FC = () => {
  const { serviceId, featureId } = useParams<{ serviceId: string; featureId?: string }>();
  const navigate = useNavigate();
  const { setCurrentService, setCurrentFeature } = useAppStore();
  const currentService = useCurrentService();
  const currentFeature = useCurrentFeature();

  // 初始化服务和功能
  useEffect(() => {
    if (serviceId) {
      setCurrentService(serviceId);
      
      // 如果有 featureId，设置当前功能
      if (featureId) {
        setCurrentFeature(featureId);
      } else {
        // 如果没有指定功能，默认选择第一个功能
        const service = getServiceById(serviceId);
        if (service && service.features.length > 0) {
          setCurrentFeature(service.features[0].id);
          navigate(`/service/${serviceId}/feature/${service.features[0].id}`, { replace: true });
        }
      }
    }
  }, [serviceId, featureId, setCurrentService, setCurrentFeature, navigate]);

  // 如果服务不存在，显示错误页面
  if (!currentService) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Icon name="AlertCircle" size={48} className="mx-auto text-red-500 mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">服务未找到</h1>
          <p className="text-gray-600 mb-4">
            请检查服务ID是否正确，或者该服务可能已被删除。
          </p>
          <button
            onClick={() => navigate('/')}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <Icon name="Home" size={16} className="mr-2" />
            返回首页
          </button>
        </div>
      </div>
    );
  }

  // 如果没有功能，显示服务概览
  if (!currentFeature) {
    return (
      <div className="min-h-screen bg-gray-50 flex">
        <Sidebar service={currentService} />
        <div className="flex-1 p-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center py-12">
              <Icon name="Zap" size={48} className="mx-auto text-gray-400 mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">选择一个功能</h2>
              <p className="text-gray-600">
                请从左侧菜单中选择一个功能来查看详细信息。
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* 侧边栏 */}
      <Sidebar 
        service={currentService} 
        currentFeatureId={currentFeature.id}
      />
      
      {/* 主内容区域 */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full overflow-y-auto">
          <FeatureContent 
            feature={currentFeature} 
            service={currentService} 
          />
        </div>
      </div>
    </div>
  );
};

export default ServiceDetailPage;
