/**
 * @license lucide-react v0.532.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M13 5H19V11", key: "1n1gyv" }],
  ["path", { d: "M19 5L5 19", key: "72u4yj" }]
];
const MoveUpRight = createLucideIcon("move-up-right", __iconNode);

export { __iconNode, MoveUpRight as default };
//# sourceMappingURL=move-up-right.js.map
